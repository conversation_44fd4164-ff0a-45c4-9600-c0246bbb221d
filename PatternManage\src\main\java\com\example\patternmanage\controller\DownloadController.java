package com.example.patternmanage.controller;

import com.example.patternmanage.dao.DownloadDao;
import com.example.patternmanage.dao.PatternDao;
import com.example.patternmanage.dao.UserDao;
import com.example.patternmanage.model.Download;
import com.example.patternmanage.model.Pattern;
import com.example.patternmanage.model.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@CrossOrigin(origins = "*")
@Controller
@RequestMapping("/downloads")
public class DownloadController {

    @Autowired
    private DownloadDao downloadDao;

    @Autowired
    private PatternDao patternDao;

    @Autowired
    private UserDao userDao;

    /**
     * Tải mẫu xuống và ghi nhận lịch sử
     */
    @PostMapping("/download/{patternId}/{userId}")
    public ResponseEntity<?> downloadPattern(@PathVariable int patternId, @PathVariable int userId) {
        try {
            // Kiểm tra user tồn tại
            User user = userDao.findById(userId);
            if (user == null) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "Người dùng không tồn tại");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }

            // Kiểm tra pattern tồn tại
            Pattern pattern = patternDao.findById(patternId);
            if (pattern == null) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "Mẫu không tồn tại");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }

            // Ghi nhận lịch sử download
            downloadDao.addDownload(patternId, userId);

            // Tạo file để download (ảnh của pattern)
            if (pattern.getImageBase64() != null && !pattern.getImageBase64().isEmpty()) {
                try {
                    // Decode base64 thành byte array
                    byte[] imageBytes = java.util.Base64.getDecoder().decode(pattern.getImageBase64());

                    // Tạo resource từ byte array
                    ByteArrayResource resource = new ByteArrayResource(imageBytes);

                    // Tạo filename
                    String filename = "pattern_" + pattern.getId() + "_" + pattern.getName().replaceAll("[^a-zA-Z0-9]", "_") + ".jpg";

                    // Tạo response headers
                    HttpHeaders headers = new HttpHeaders();
                    headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"");
                    headers.add(HttpHeaders.CONTENT_TYPE, MediaType.IMAGE_JPEG_VALUE);

                    return ResponseEntity.ok()
                            .headers(headers)
                            .contentLength(imageBytes.length)
                            .contentType(MediaType.IMAGE_JPEG)
                            .body(resource);

                } catch (Exception e) {
                    Map<String, Object> response = new HashMap<>();
                    response.put("success", false);
                    response.put("message", "Lỗi khi xử lý file ảnh: " + e.getMessage());
                    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
                }
            } else {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "Mẫu không có ảnh để tải xuống");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }

        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Lỗi khi tải mẫu: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Lấy lịch sử download của user với phân trang và thông tin pattern đầy đủ
     */
    @GetMapping("/user/{userId}")
    public ResponseEntity<?> getUserDownloads(@PathVariable int userId,
                                            @RequestParam(defaultValue = "1") int page,
                                            @RequestParam(defaultValue = "12") int size) {
        try {
            // Kiểm tra user tồn tại
            User user = userDao.findById(userId);
            if (user == null) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "Người dùng không tồn tại");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }

            // Lấy downloads với phân trang (giả sử có method này trong DAO)
            List<Download> allDownloads = downloadDao.getDownloadsByUser(userId);

            // Tính toán phân trang thủ công
            int totalItems = allDownloads.size();
            int totalPages = (int) Math.ceil((double) totalItems / size);
            int startIndex = (page - 1) * size;
            int endIndex = Math.min(startIndex + size, totalItems);

            List<Download> pagedDownloads = allDownloads.subList(startIndex, endIndex);

            // Tạo response với thông tin pattern đầy đủ
            List<Map<String, Object>> downloadList = new ArrayList<>();
            for (Download download : pagedDownloads) {
                Map<String, Object> downloadInfo = new HashMap<>();
                downloadInfo.put("id", download.getId());
                downloadInfo.put("downloadDate", download.getDownloadDate());
                downloadInfo.put("createdAt", download.getCreatedAt());
                downloadInfo.put("updatedAt", download.getUpdatedAt());

                // Lấy thông tin pattern đầy đủ
                Pattern pattern = patternDao.findById(download.getPatternId());
                if (pattern != null) {
                    Map<String, Object> patternInfo = new HashMap<>();
                    patternInfo.put("id", pattern.getId());
                    patternInfo.put("name", pattern.getName());
                    patternInfo.put("label", pattern.getLabel());
                    patternInfo.put("description", pattern.getDescription());
                    patternInfo.put("imageBase64", pattern.getImageBase64());
                    patternInfo.put("createdAt", pattern.getCreatedAt());
                    patternInfo.put("updatedAt", pattern.getUpdatedAt());

                    downloadInfo.put("pattern", patternInfo);
                } else {
                    // Pattern đã bị xóa
                    Map<String, Object> patternInfo = new HashMap<>();
                    patternInfo.put("id", download.getPatternId());
                    patternInfo.put("name", "Mẫu đã bị xóa");
                    patternInfo.put("label", 0);
                    patternInfo.put("description", "Mẫu này đã bị xóa khỏi hệ thống");
                    patternInfo.put("imageBase64", null);

                    downloadInfo.put("pattern", patternInfo);
                }

                downloadList.add(downloadInfo);
            }

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("downloads", downloadList);
            response.put("page", page);
            response.put("size", size);
            response.put("total", totalItems);
            response.put("totalPages", totalPages);
            response.put("hasNext", page < totalPages);
            response.put("hasPrevious", page > 1);
            response.put("message", "Lấy lịch sử download thành công");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Lỗi khi lấy lịch sử download: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Lấy thống kê download của pattern
     */
    @GetMapping("/pattern/{patternId}")
    public ResponseEntity<?> getPatternDownloads(@PathVariable int patternId) {
        try {
            // Kiểm tra pattern tồn tại
            Pattern pattern = patternDao.findById(patternId);
            if (pattern == null) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "Mẫu không tồn tại");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }

            List<Download> downloads = downloadDao.getDownloadsByPattern(patternId);
            int downloadCount = downloadDao.getDownloadCount(patternId);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("downloads", downloads);
            response.put("downloadCount", downloadCount);
            response.put("message", "Lấy thống kê download thành công");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Lỗi khi lấy thống kê download: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Kiểm tra user đã download pattern chưa
     */
    @GetMapping("/check/{patternId}/{userId}")
    public ResponseEntity<?> checkDownloaded(@PathVariable int patternId, @PathVariable int userId) {
        try {
            boolean hasDownloaded = downloadDao.hasUserDownloaded(patternId, userId);
            int downloadCount = downloadDao.getDownloadCount(patternId);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("hasDownloaded", hasDownloaded);
            response.put("downloadCount", downloadCount);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Lỗi khi kiểm tra trạng thái download: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Lấy tất cả downloads (CHỈ ADMIN)
     */
    @GetMapping("/all/{adminId}")
    public ResponseEntity<?> getAllDownloads(@PathVariable int adminId) {
        try {
            // Kiểm tra quyền admin
            if (!userDao.isAdmin(adminId)) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "Bạn không có quyền xem danh sách download");
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
            }

            List<Download> downloads = downloadDao.findAll();

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("downloads", downloads);
            response.put("total", downloads.size());
            response.put("message", "Lấy danh sách download thành công");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Lỗi khi lấy danh sách download: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Lấy thống kê tổng quan download
     */
    @GetMapping("/statistics/{adminId}")
    public ResponseEntity<?> getDownloadStatistics(@PathVariable int adminId) {
        try {
            // Kiểm tra quyền admin
            if (!userDao.isAdmin(adminId)) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "Bạn không có quyền xem thống kê");
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
            }

            int totalDownloads = downloadDao.getTotalDownloads();
            List<Object[]> topPatterns = downloadDao.getTopDownloadedPatterns(10);

            Map<String, Object> statistics = new HashMap<>();
            statistics.put("totalDownloads", totalDownloads);
            statistics.put("topPatterns", topPatterns);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("statistics", statistics);
            response.put("message", "Lấy thống kê thành công");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Lỗi khi lấy thống kê: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Xóa download cụ thể theo ID (User có thể xóa download của mình, Admin có thể xóa bất kỳ)
     */
    @DeleteMapping("/remove/{downloadId}/{requesterId}")
    public ResponseEntity<?> removeSpecificDownload(@PathVariable int downloadId, @PathVariable int requesterId) {
        try {
            // Kiểm tra download tồn tại
            Download download = downloadDao.findById(downloadId);
            if (download == null) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "Không tìm thấy download");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }

            // Kiểm tra user tồn tại
            User requester = userDao.findById(requesterId);
            if (requester == null) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "Người dùng không tồn tại");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }

            // Kiểm tra quyền: chỉ admin hoặc chính user đó mới được xóa
            boolean isAdmin = userDao.isAdmin(requesterId);
            boolean isOwner = download.getUserId() == requesterId;

            if (!isAdmin && !isOwner) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "Bạn không có quyền xóa download này");
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
            }

            // Xóa download cụ thể
            downloadDao.delete(downloadId);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Đã xóa mẫu khỏi danh sách đã tải");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Lỗi khi xóa download: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Xóa TẤT CẢ downloads của user cho pattern cụ thể (deprecated - chỉ để tương thích)
     */
    @DeleteMapping("/remove-all/{patternId}/{userId}")
    public ResponseEntity<?> removeAllDownloads(@PathVariable int patternId, @PathVariable int userId) {
        try {
            // Kiểm tra user tồn tại
            User user = userDao.findById(userId);
            if (user == null) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "Người dùng không tồn tại");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }

            // Kiểm tra pattern tồn tại
            Pattern pattern = patternDao.findById(patternId);
            if (pattern == null) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "Mẫu không tồn tại");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }

            // Kiểm tra user đã download pattern này chưa
            if (!downloadDao.hasUserDownloaded(patternId, userId)) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "Bạn chưa tải mẫu này");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }

            // Xóa TẤT CẢ download records của user cho pattern này
            downloadDao.removeDownload(patternId, userId);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Đã xóa tất cả bản sao của mẫu khỏi danh sách đã tải");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Lỗi khi xóa download: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Xóa download theo ID (Admin only)
     */
    @DeleteMapping("/delete/{downloadId}/{adminId}")
    public ResponseEntity<?> deleteDownloadById(@PathVariable int downloadId, @PathVariable int adminId) {
        try {
            // Kiểm tra quyền admin
            if (!userDao.isAdmin(adminId)) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "Bạn không có quyền thực hiện thao tác này");
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
            }

            // Kiểm tra download tồn tại
            Download download = downloadDao.findById(downloadId);
            if (download == null) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "Không tìm thấy download");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }

            // Xóa download
            downloadDao.delete(downloadId);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Đã xóa download thành công");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Lỗi khi xóa download: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
}
