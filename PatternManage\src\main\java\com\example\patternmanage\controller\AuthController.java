package com.example.patternmanage.controller;

import com.example.patternmanage.dao.UserDao;
import com.example.patternmanage.model.User;
import com.example.patternmanage.model.UserRole;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@CrossOrigin(origins = "*")
@Controller
@RequestMapping("/auth")
public class AuthController {

    @Autowired
    private UserDao userDao;

    /**
     * Tạo response user (không bao gồm password)
     */
    private Map<String, Object> createUserResponse(User user) {
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("id", user.getId());
        userInfo.put("username", user.getUsername());
        userInfo.put("email", user.getEmail());
        userInfo.put("name", user.getName());
        userInfo.put("phoneNumber", user.getPhoneNumber());
        userInfo.put("role", user.getRole().name());
        userInfo.put("roleDisplayName", user.getRole().getDisplayName());
        userInfo.put("isAdmin", user.getRole().isAdmin());
        userInfo.put("isActive", user.isActive());
        userInfo.put("createdAt", user.getCreatedAt());
        return userInfo;
    }

    /**
     * Đăng ký người dùng mới (USER)
     */
    @PostMapping("/register")
    public ResponseEntity<?> register(@RequestParam("username") String username,
                                    @RequestParam("email") String email,
                                    @RequestParam("password") String password,
                                    @RequestParam(value = "name", required = false) String name,
                                    @RequestParam(value = "phoneNumber", required = false) String phoneNumber) {
        
        // Validate input
        if (username == null || username.trim().isEmpty()) {
            return ResponseEntity.badRequest().body("Tên người dùng không được để trống");
        }
        if (email == null || email.trim().isEmpty()) {
            return ResponseEntity.badRequest().body("Email không được để trống");
        }
        if (password == null || password.length() < 6) {
            return ResponseEntity.badRequest().body("Mật khẩu phải có ít nhất 6 ký tự");
        }

        try {
            // Kiểm tra username hoặc email đã tồn tại
            if (userDao.existsByUsernameOrEmail(username.trim(), email.trim())) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "Tên người dùng hoặc email đã tồn tại");
                return ResponseEntity.status(HttpStatus.CONFLICT).body(result);
            }

            // Tạo user mới với role USER
            userDao.saveOrUpdate(null, username.trim(), email.trim(), password, name, phoneNumber, UserRole.USER);
            
            // Lấy thông tin user vừa tạo
            User newUser = userDao.findByUsername(username.trim());
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "Đăng ký thành công");
            result.put("user", createUserResponse(newUser));
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "Lỗi khi đăng ký: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * Đăng nhập
     */
    @PostMapping("/login")
    public ResponseEntity<?> login(@RequestParam("username") String username,
                                 @RequestParam("password") String password) {
        
        // Validate input
        if (username == null || username.trim().isEmpty()) {
            return ResponseEntity.badRequest().body("Tên người dùng không được để trống");
        }
        if (password == null || password.isEmpty()) {
            return ResponseEntity.badRequest().body("Mật khẩu không được để trống");
        }

        try {
            // Tìm user theo username
            User user = userDao.findByUsername(username.trim());
            
            Map<String, Object> result = new HashMap<>();
            
            if (user == null) {
                result.put("success", false);
                result.put("message", "Tên người dùng không tồn tại");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(result);
            }

            // Kiểm tra tài khoản có bị vô hiệu hóa không
            if (!user.isActive()) {
                result.put("success", false);
                result.put("message", "Tài khoản đã bị vô hiệu hóa");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(result);
            }

            // Kiểm tra mật khẩu (trong thực tế nên so sánh hash)
            if (!user.getPassword().equals(password)) {
                result.put("success", false);
                result.put("message", "Mật khẩu không chính xác");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(result);
            }

            // Đăng nhập thành công
            result.put("success", true);
            result.put("message", "Đăng nhập thành công");
            result.put("user", createUserResponse(user));
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "Lỗi khi đăng nhập: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * Tạo tài khoản admin mới (CHỈ ADMIN)
     */
    @PostMapping("/create-admin")
    public ResponseEntity<?> createAdmin(@RequestParam("username") String username,
                                       @RequestParam("email") String email,
                                       @RequestParam("password") String password,
                                       @RequestParam(value = "name", required = false) String name,
                                       @RequestParam(value = "phoneNumber", required = false) String phoneNumber,
                                       @RequestParam("adminId") int adminId) {
        
        // Validate input
        if (username == null || username.trim().isEmpty()) {
            return ResponseEntity.badRequest().body("Tên người dùng không được để trống");
        }
        if (email == null || email.trim().isEmpty()) {
            return ResponseEntity.badRequest().body("Email không được để trống");
        }
        if (password == null || password.length() < 6) {
            return ResponseEntity.badRequest().body("Mật khẩu phải có ít nhất 6 ký tự");
        }

        try {
            // Kiểm tra quyền admin
            if (!userDao.isAdmin(adminId)) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "Bạn không có quyền tạo tài khoản admin");
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(result);
            }

            // Kiểm tra username hoặc email đã tồn tại
            if (userDao.existsByUsernameOrEmail(username.trim(), email.trim())) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "Tên người dùng hoặc email đã tồn tại");
                return ResponseEntity.status(HttpStatus.CONFLICT).body(result);
            }

            // Tạo admin mới
            userDao.saveOrUpdate(null, username.trim(), email.trim(), password, name, phoneNumber, UserRole.ADMIN);
            
            // Lấy thông tin admin vừa tạo
            User newAdmin = userDao.findByUsername(username.trim());
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "Tạo tài khoản admin thành công");
            result.put("user", createUserResponse(newAdmin));
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "Lỗi khi tạo admin: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * Lấy thông tin người dùng hiện tại
     */
    @GetMapping("/me/{userId}")
    public ResponseEntity<?> getCurrentUser(@PathVariable int userId) {
        try {
            User user = userDao.findById(userId);
            Map<String, Object> result = new HashMap<>();
            
            if (user == null) {
                result.put("success", false);
                result.put("message", "Không tìm thấy người dùng");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(result);
            }

            result.put("success", true);
            result.put("user", createUserResponse(user));
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "Lỗi khi lấy thông tin: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * Kiểm tra quyền admin
     */
    @GetMapping("/check-admin/{userId}")
    public ResponseEntity<?> checkAdmin(@PathVariable int userId) {
        try {
            boolean isAdmin = userDao.isAdmin(userId);
            Map<String, Object> result = new HashMap<>();
            result.put("isAdmin", isAdmin);
            result.put("message", isAdmin ? "Người dùng có quyền admin" : "Người dùng không có quyền admin");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Lỗi khi kiểm tra quyền: " + e.getMessage());
        }
    }

    /**
     * Đổi mật khẩu
     */
    @PostMapping("/change-password")
    public ResponseEntity<?> changePassword(@RequestParam("userId") int userId,
                                          @RequestParam("oldPassword") String oldPassword,
                                          @RequestParam("newPassword") String newPassword) {
        
        // Validate input
        if (oldPassword == null || oldPassword.isEmpty()) {
            return ResponseEntity.badRequest().body("Mật khẩu cũ không được để trống");
        }
        if (newPassword == null || newPassword.length() < 6) {
            return ResponseEntity.badRequest().body("Mật khẩu mới phải có ít nhất 6 ký tự");
        }

        try {
            User user = userDao.findById(userId);
            Map<String, Object> result = new HashMap<>();
            
            if (user == null) {
                result.put("success", false);
                result.put("message", "Không tìm thấy người dùng");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(result);
            }

            // Kiểm tra mật khẩu cũ
            if (!user.getPassword().equals(oldPassword)) {
                result.put("success", false);
                result.put("message", "Mật khẩu cũ không chính xác");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(result);
            }

            // Cập nhật mật khẩu mới
            userDao.saveOrUpdate(userId, user.getUsername(), user.getEmail(), newPassword,
                               user.getName(), user.getPhoneNumber(), user.getRole());

            result.put("success", true);
            result.put("message", "Đổi mật khẩu thành công");
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "Lỗi khi đổi mật khẩu: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * Đăng xuất (đơn giản - chỉ trả về thông báo)
     */
    @PostMapping("/logout")
    public ResponseEntity<?> logout() {
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", "Đăng xuất thành công");
        return ResponseEntity.ok(result);
    }

    /**
     * Kiểm tra trạng thái đăng nhập
     */
    @GetMapping("/status/{userId}")
    public ResponseEntity<?> checkLoginStatus(@PathVariable int userId) {
        try {
            User user = userDao.findById(userId);
            Map<String, Object> result = new HashMap<>();
            
            if (user != null && user.isActive()) {
                Map<String, Object> userInfo = createUserResponse(user);
                result.put("isLoggedIn", true);
                result.put("user", userInfo);
                result.put("isAdmin", userInfo.get("isAdmin"));
                return ResponseEntity.ok(result);
            } else {
                result.put("isLoggedIn", false);
                result.put("message", "Người dùng chưa đăng nhập hoặc không tồn tại");
                return ResponseEntity.ok(result);
            }
        } catch (Exception e) {
            Map<String, Object> result = new HashMap<>();
            result.put("isLoggedIn", false);
            result.put("message", "Lỗi khi kiểm tra trạng thái: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }
}
