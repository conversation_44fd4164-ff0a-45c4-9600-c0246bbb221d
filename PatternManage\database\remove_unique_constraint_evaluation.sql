-- Script để xóa unique constraint trên bảng evaluation
-- Cho phép user đánh giá nhiều lần cho cùng 1 pattern

-- <PERSON><PERSON><PERSON> tra xem constraint có tồn tại không
SELECT CONSTRAINT_NAME 
FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'evaluation' 
  AND CONSTRAINT_TYPE = 'UNIQUE';

-- Xóa unique constraint
ALTER TABLE evaluation DROP INDEX unique_user_pattern;

-- Kiểm tra lại sau khi xóa
SELECT CONSTRAINT_NAME 
FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'evaluation' 
  AND CONSTRAINT_TYPE = 'UNIQUE';

-- <PERSON><PERSON><PERSON> thị cấu trúc bảng sau khi thay đổi
DESCRIBE evaluation;
