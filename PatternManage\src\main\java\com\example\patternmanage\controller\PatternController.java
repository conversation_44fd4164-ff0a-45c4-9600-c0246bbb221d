package com.example.patternmanage.controller;

import com.example.patternmanage.dao.PatternDao;
import com.example.patternmanage.model.Pattern;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@CrossOrigin(origins = "*")
@Controller
@RequestMapping("/patterns")
public class PatternController {

    @Autowired
    private PatternDao patternDao;

    @GetMapping("/search")
    public ResponseEntity<?> searchPatterns(@RequestParam("keyword") String keyword) {
        try {
            List<Pattern> results = patternDao.searchByName(keyword);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("patterns", results);
            response.put("total", results.size());
            response.put("keyword", keyword);
            response.put("message", results.isEmpty() ? "Không tìm thấy mẫu nào" : "Tìm kiếm thành công");

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Lỗi khi tìm kiếm: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }


    @PostMapping("/add")
    public ResponseEntity<?> addPattern(@RequestParam("name") String name,
                                        @RequestParam("label") int label,
                                        @RequestParam(value = "image", required = false) MultipartFile imageFile) {
        try {
            Map<String, Object> response = new HashMap<>();

            // Validate input
            if (name == null || name.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "Tên mẫu không được để trống");
                return ResponseEntity.badRequest().body(response);
            }

            String base64Image = null;
            if (imageFile != null && !imageFile.isEmpty()) {
                byte[] imageBytes = imageFile.getBytes();
                base64Image = Base64.getEncoder().encodeToString(imageBytes);

                if (patternDao.existsByImageBase64OrName(base64Image, name.trim())) {
                    response.put("success", false);
                    response.put("message", "Tên hoặc ảnh đã tồn tại trong cơ sở dữ liệu");
                    return ResponseEntity.status(HttpStatus.CONFLICT).body(response);
                }
            }

            String description = (label == 1) ? "Bạo lực" : "Không bạo lực";
            patternDao.saveOrUpdate(null, name.trim(), label, base64Image, description);

            response.put("success", true);
            response.put("message", "Thêm mẫu thành công");
            return ResponseEntity.ok(response);
        } catch (IOException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Lỗi xử lý file: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        } catch (Exception ex) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Lỗi khi thêm mẫu: " + ex.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @GetMapping("/all")
    public ResponseEntity<?> getAllPatterns(
            @RequestParam(value = "page", defaultValue = "1") int page,
            @RequestParam(value = "size", defaultValue = "5") int size) {
        try {
            // Validate parameters
            if (page < 1) page = 1;
            if (size < 1) size = 10;
            if (size > 100) size = 100; // Giới hạn tối đa 100 items per page

            List<Pattern> allPatterns = patternDao.findAll();
            int totalItems = allPatterns.size();
            int totalPages = (int) Math.ceil((double) totalItems / size);

            // Calculate pagination
            int startIndex = (page - 1) * size;
            int endIndex = Math.min(startIndex + size, totalItems);

            List<Pattern> paginatedPatterns;
            if (startIndex >= totalItems) {
                paginatedPatterns = new ArrayList<>();
            } else {
                paginatedPatterns = allPatterns.subList(startIndex, endIndex);
            }

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("patterns", paginatedPatterns);
            response.put("total", totalItems);
            response.put("page", page);
            response.put("size", size);
            response.put("totalPages", totalPages);
            response.put("hasNext", page < totalPages);
            response.put("hasPrevious", page > 1);
            response.put("message", "Lấy danh sách mẫu thành công");

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Lỗi khi lấy danh sách mẫu: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @PostMapping("/update")
    public ResponseEntity<?> updatePattern(@RequestParam("id") int id,
                                           @RequestParam("name") String name,
                                           @RequestParam("label") int label,
                                           @RequestParam(value = "image", required = false) MultipartFile imageFile) {
        try {
            Map<String, Object> response = new HashMap<>();

            // Validate input
            if (name == null || name.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "Tên mẫu không được để trống");
                return ResponseEntity.badRequest().body(response);
            }

            Pattern pattern = patternDao.findById(id);
            if (pattern == null) {
                response.put("success", false);
                response.put("message", "Không tìm thấy mẫu với ID: " + id);
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }

            String base64Image = pattern.getImageBase64();
            String description = (label == 1) ? "Bạo lực" : "Không bạo lực";

            // Xử lý ảnh mới nếu có
            if (imageFile != null && !imageFile.isEmpty()) {
                byte[] imageBytes = imageFile.getBytes();
                base64Image = Base64.getEncoder().encodeToString(imageBytes);

                if (patternDao.existsByImageBase64ExcludingId(base64Image, id)) {
                    response.put("success", false);
                    response.put("message", "Ảnh đã tồn tại trong cơ sở dữ liệu");
                    return ResponseEntity.status(HttpStatus.CONFLICT).body(response);
                }
            }

            patternDao.saveOrUpdate(id, name.trim(), label, base64Image, description);

            response.put("success", true);
            response.put("message", "Cập nhật mẫu thành công");
            return ResponseEntity.ok(response);

        } catch (IOException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Lỗi xử lý ảnh: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        } catch (Exception ex) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Lỗi cập nhật mẫu: " + ex.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<?> getPatternById(@PathVariable int id) {
        Pattern pattern = patternDao.findById(id);
        if (pattern == null) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body("Không tìm thấy mẫu với ID: " + id);
        }
        return ResponseEntity.ok(pattern);
    }


    @DeleteMapping("/delete/{id}")
    public ResponseEntity<?> deletePatternById(@PathVariable int id) {
        try {
            Pattern pattern = patternDao.findById(id);
            Map<String, Object> response = new HashMap<>();

            if (pattern == null) {
                response.put("success", false);
                response.put("message", "Không tìm thấy mẫu với ID: " + id);
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }

            patternDao.deleteById(id);

            response.put("success", true);
            response.put("message", "Xóa mẫu thành công");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Lỗi khi xóa mẫu: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @GetMapping("/filter")
    public ResponseEntity<?> filterPatterns(
            @RequestParam(value = "label", required = false) String labelStr,
            @RequestParam(value = "startDate", required = false) String startDateStr,
            @RequestParam(value = "endDate", required = false) String endDateStr) {

        try {
            Integer label = null;

            // Parse label từ string sang number
            if (labelStr != null && !labelStr.isEmpty()) {
                try {
                    label = Integer.parseInt(labelStr);
                    // Validate label value
                    if (label != 0 && label != 1) {
                        Map<String, Object> response = new HashMap<>();
                        response.put("success", false);
                        response.put("message", "Giá trị label không hợp lệ. Chỉ chấp nhận 0 hoặc 1");
                        return ResponseEntity.badRequest().body(response);
                    }
                } catch (NumberFormatException e) {
                    Map<String, Object> response = new HashMap<>();
                    response.put("success", false);
                    response.put("message", "Giá trị label không hợp lệ. Chỉ chấp nhận số 0 hoặc 1");
                    return ResponseEntity.badRequest().body(response);
                }
            }

            LocalDate startDate = null;
            if (startDateStr != null && !startDateStr.isEmpty()) {
                startDate = LocalDate.parse(startDateStr);
            }

            LocalDate endDate = null;
            if (endDateStr != null && !endDateStr.isEmpty()) {
                endDate = LocalDate.parse(endDateStr);
            }

            List<Pattern> filteredPatterns = patternDao.filterPatterns(label, startDate, endDate);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("patterns", filteredPatterns);
            response.put("total", filteredPatterns.size());

            Map<String, String> filters = new HashMap<>();
            filters.put("label", labelStr != null ? labelStr : "all");
            filters.put("startDate", startDateStr != null ? startDateStr : "");
            filters.put("endDate", endDateStr != null ? endDateStr : "");
            response.put("filters", filters);

            response.put("message", filteredPatterns.isEmpty() ? "Không tìm thấy mẫu nào phù hợp" : "Lọc thành công");

            return ResponseEntity.ok(response);
        } catch (DateTimeParseException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Định dạng ngày không hợp lệ. Sử dụng định dạng yyyy-MM-dd");
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Lỗi khi lọc mẫu: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
}
