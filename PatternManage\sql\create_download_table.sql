-- =====================================================
-- SQL Script tạo bảng DOWNLOAD
-- Liên kết với bảng USERS và PATTERNS có sẵn
-- =====================================================

-- Sử dụng database pattern_management
USE pattern_management;

-- Kiểm tra và hiển thị các bảng hiện có
SHOW TABLES;

-- Tạ<PERSON> bảng DOWNLOAD
CREATE TABLE IF NOT EXISTS download (
    -- Kh<PERSON>a chính
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'ID tự tăng của bản ghi download',

    -- Khóa ngoại liên kết với bảng patterns
    pattern_id INT NOT NULL COMMENT 'ID của mẫu được tải (FK đến patterns.id)',

    -- <PERSON><PERSON><PERSON><PERSON> ngoại liên kết với bảng users
    user_id INT NOT NULL COMMENT 'ID của người dùng tải mẫu (FK đến users.id)',

    -- Thời gian download
    download_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Thời gian tải mẫu',

    -- Metadata timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Thời gian tạo bản ghi',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Thời gian cập nhật bản ghi',

    -- Ràng buộc khóa ngoại
    CONSTRAINT fk_download_pattern
        FOREIGN KEY (pattern_id) REFERENCES pattern(id)
        ON DELETE CASCADE
        ON UPDATE CASCADE,

    CONSTRAINT fk_download_user
        FOREIGN KEY (user_id) REFERENCES user(id)
        ON DELETE CASCADE
        ON UPDATE CASCADE,

    -- Index để tối ưu truy vấn
    INDEX idx_pattern_id (pattern_id),
    INDEX idx_user_id (user_id),
    INDEX idx_download_date (download_date),
    INDEX idx_pattern_user (pattern_id, user_id),
    INDEX idx_user_date (user_id, download_date)

) ENGINE=InnoDB
  DEFAULT CHARSET=utf8mb4
  COLLATE=utf8mb4_unicode_ci
  COMMENT='Bảng lưu trữ lịch sử tải mẫu của người dùng';

-- Kiểm tra bảng đã được tạo
DESCRIBE download;

-- Thêm một số dữ liệu mẫu (tùy chọn)
-- Chỉ chạy nếu có dữ liệu trong bảng pattern và user
INSERT INTO download (pattern_id, user_id, download_date)
SELECT
    p.id as pattern_id,
    u.id as user_id,
    NOW() as download_date
FROM pattern p
CROSS JOIN user u
WHERE p.id <= 3 AND u.id <= 2
LIMIT 5;

-- Hiển thị dữ liệu mẫu
SELECT
    d.id,
    d.pattern_id,
    p.name as pattern_name,
    d.user_id,
    u.username,
    d.download_date
FROM download d
JOIN pattern p ON d.pattern_id = p.id
JOIN user u ON d.user_id = u.id
ORDER BY d.download_date DESC
LIMIT 10;

-- Thống kê cơ bản
SELECT
    'Total Downloads' as metric,
    COUNT(*) as value
FROM download

UNION ALL

SELECT
    'Unique Users Downloaded' as metric,
    COUNT(DISTINCT user_id) as value
FROM download

UNION ALL

SELECT
    'Unique Patterns Downloaded' as metric,
    COUNT(DISTINCT pattern_id) as value
FROM download;

-- Top 5 patterns được download nhiều nhất
SELECT
    p.id,
    p.name as pattern_name,
    COUNT(d.id) as download_count
FROM pattern p
LEFT JOIN download d ON p.id = d.pattern_id
GROUP BY p.id, p.name
ORDER BY download_count DESC
LIMIT 5;

-- Top 5 users download nhiều nhất
SELECT
    u.id,
    u.username,
    COUNT(d.id) as download_count
FROM user u
LEFT JOIN download d ON u.id = d.user_id
GROUP BY u.id, u.username
ORDER BY download_count DESC
LIMIT 5;

SELECT 'Download table created successfully!' as status;

-- =====================================================
-- SQL Script tạo bảng EVALUATION
-- Lưu trữ đánh giá thành công/không thành công của người dùng
-- =====================================================

-- Tạo bảng EVALUATION
CREATE TABLE IF NOT EXISTS evaluation (
    -- Khóa chính
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'ID tự tăng của bản ghi evaluation',

    -- Khóa ngoại liên kết với bảng users
    user_id INT NOT NULL COMMENT 'ID của người dùng đánh giá (FK đến users.id)',

    -- Khóa ngoại liên kết với bảng patterns
    pattern_id INT NOT NULL COMMENT 'ID của mẫu được đánh giá (FK đến patterns.id)',

    -- Mô tả đánh giá (chỉ 2 lựa chọn)
    description ENUM('Thành công', 'Không thành công') NOT NULL COMMENT 'Kết quả đánh giá',

    -- Thời gian đánh giá
    evaluation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Thời gian đánh giá',

    -- Metadata timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Thời gian tạo bản ghi',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Thời gian cập nhật bản ghi',

    -- Ràng buộc khóa ngoại
    CONSTRAINT fk_evaluation_user
        FOREIGN KEY (user_id) REFERENCES user(id)
        ON DELETE CASCADE
        ON UPDATE CASCADE,

    CONSTRAINT fk_evaluation_pattern
        FOREIGN KEY (pattern_id) REFERENCES pattern(id)
        ON DELETE CASCADE
        ON UPDATE CASCADE,

    -- Ràng buộc unique: mỗi user chỉ đánh giá 1 lần cho 1 pattern
    UNIQUE KEY unique_user_pattern (user_id, pattern_id),

    -- Index để tối ưu truy vấn
    INDEX idx_user_id (user_id),
    INDEX idx_pattern_id (pattern_id),
    INDEX idx_evaluation_date (evaluation_date),
    INDEX idx_description (description),
    INDEX idx_pattern_description (pattern_id, description)

) ENGINE=InnoDB
  DEFAULT CHARSET=utf8mb4
  COLLATE=utf8mb4_unicode_ci
  COMMENT='Bảng lưu trữ đánh giá thành công/không thành công của người dùng';

-- Kiểm tra bảng đã được tạo
DESCRIBE evaluation;

-- Thêm một số dữ liệu mẫu (tùy chọn)
-- Chỉ chạy nếu có dữ liệu trong bảng pattern và user
INSERT INTO evaluation (user_id, pattern_id, description, evaluation_date)
SELECT
    u.id as user_id,
    p.id as pattern_id,
    CASE
        WHEN RAND() > 0.3 THEN 'Thành công'
        ELSE 'Không thành công'
    END as description,
    NOW() as evaluation_date
FROM user u
CROSS JOIN pattern p
WHERE u.id <= 3 AND p.id <= 3
LIMIT 6;

-- Hiển thị dữ liệu mẫu
SELECT
    e.id,
    e.user_id,
    u.username,
    e.pattern_id,
    p.name as pattern_name,
    e.description,
    e.evaluation_date
FROM evaluation e
JOIN user u ON e.user_id = u.id
JOIN pattern p ON e.pattern_id = p.id
ORDER BY e.evaluation_date DESC
LIMIT 10;

-- Thống kê cơ bản
SELECT
    'Total Evaluations' as metric,
    COUNT(*) as value
FROM evaluation

UNION ALL

SELECT
    'Success Evaluations' as metric,
    COUNT(*) as value
FROM evaluation
WHERE description = 'Thành công'

UNION ALL

SELECT
    'Failed Evaluations' as metric,
    COUNT(*) as value
FROM evaluation
WHERE description = 'Không thành công'

UNION ALL

SELECT
    'Success Rate (%)' as metric,
    ROUND(
        (COUNT(CASE WHEN description = 'Thành công' THEN 1 END) * 100.0 / COUNT(*)), 2
    ) as value
FROM evaluation;

-- Top 5 patterns có tỷ lệ thành công cao nhất
SELECT
    p.id,
    p.name as pattern_name,
    COUNT(e.id) as total_evaluations,
    COUNT(CASE WHEN e.description = 'Thành công' THEN 1 END) as success_count,
    COUNT(CASE WHEN e.description = 'Không thành công' THEN 1 END) as failed_count,
    ROUND(
        (COUNT(CASE WHEN e.description = 'Thành công' THEN 1 END) * 100.0 / COUNT(e.id)), 2
    ) as success_rate_percent
FROM pattern p
LEFT JOIN evaluation e ON p.id = e.pattern_id
GROUP BY p.id, p.name
HAVING total_evaluations > 0
ORDER BY success_rate_percent DESC, total_evaluations DESC
LIMIT 5;

-- Top 5 users đánh giá nhiều nhất
SELECT
    u.id,
    u.username,
    COUNT(e.id) as total_evaluations,
    COUNT(CASE WHEN e.description = 'Thành công' THEN 1 END) as success_count,
    COUNT(CASE WHEN e.description = 'Không thành công' THEN 1 END) as failed_count
FROM user u
LEFT JOIN evaluation e ON u.id = e.user_id
GROUP BY u.id, u.username
HAVING total_evaluations > 0
ORDER BY total_evaluations DESC
LIMIT 5;

SELECT 'Evaluation table created successfully!' as status;
