package com.example.patternmanage.dao;

import com.example.patternmanage.model.Pattern;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.TypedQuery;
import javax.transaction.Transactional;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

@Service
public class PatternDao {
    @PersistenceContext
    private EntityManager entityManager;

    public List<Pattern> searchByName(String keyword) {
        String jpql = "SELECT p FROM Pattern p WHERE LOWER(p.name) LIKE LOWER(CONCAT('%', :keyword, '%'))";
        return entityManager.createQuery(jpql, Pattern.class)
                .setParameter("keyword", keyword)
                .getResultList();
    }

    public List<Pattern> findAll() {
        String jpql = "SELECT p FROM Pattern p";
        return entityManager.createQuery(jpql, Pattern.class)
                .getResultList();
    }

    @Transactional
    public void saveOrUpdate(Integer id, String name, int label, String imageBase64, String description) {
        Pattern pattern;
        if (id != null) {
            pattern = entityManager.find(Pattern.class, id);

        } else {
            pattern = new Pattern();
            pattern.setCreatedAt(LocalDateTime.now());
        }
        pattern.setName(name);
        pattern.setImageBase64(imageBase64);
        pattern.setLabel(label);
        pattern.setDescription(description);

        if (id == null) {
            entityManager.persist(pattern);
        } else {
            entityManager.merge(pattern);
        }
    }

    @Transactional
    public void deleteById(int id) {
        Pattern pattern = entityManager.find(Pattern.class, id);
        if (pattern != null) {
            entityManager.remove(pattern);
        }
    }

    public boolean existsByImageBase64OrName(String base64, String name) {
        String jpql = "SELECT COUNT(p) FROM Pattern p WHERE p.imageBase64 = :base64 OR p.name = :name";
        Long count = entityManager.createQuery(jpql, Long.class)
                .setParameter("base64", base64)
                .setParameter("name", name)
                .getSingleResult();
        return count > 0;
    }

    public boolean existsByImageBase64ExcludingId(String base64, int excludeId) {
        String jpql = "SELECT COUNT(p) FROM Pattern p WHERE p.imageBase64 = :base64 AND p.id <> :excludeId";
        Long count = entityManager.createQuery(jpql, Long.class)
                .setParameter("base64", base64)
                .setParameter("excludeId", excludeId)
                .getSingleResult();
        return count > 0;
    }


    public Pattern findById(int id) {
        return entityManager.find(Pattern.class, id);
    }

    public List<Pattern> filterPatterns(Integer label, LocalDate startDate, LocalDate endDate) {
        StringBuilder jpqlBuilder = new StringBuilder("SELECT p FROM Pattern p WHERE 1=1 ");
        Map<String, Object> parameters = new HashMap<>();

        if (label != null) {
            jpqlBuilder.append("AND p.label = :label ");
            parameters.put("label", label);
        }

        if (startDate != null) {
            jpqlBuilder.append("AND p.createdAt >= :startDateTime ");
            parameters.put("startDateTime", startDate.atStartOfDay());
        }

        if (endDate != null) {
            jpqlBuilder.append("AND p.createdAt <= :endDateTime ");
            parameters.put("endDateTime", endDate.plusDays(1).atStartOfDay().minusNanos(1));
        }

        jpqlBuilder.append("ORDER BY p.createdAt ASC");

        TypedQuery<Pattern> query = entityManager.createQuery(jpqlBuilder.toString(), Pattern.class);

        for (Map.Entry<String, Object> entry : parameters.entrySet()) {
            query.setParameter(entry.getKey(), entry.getValue());
        }

        return query.getResultList();
    }

    /**
     * Đếm tổng số patterns
     */
    public long getTotalPatterns() {
        String jpql = "SELECT COUNT(p) FROM Pattern p";
        return entityManager.createQuery(jpql, Long.class).getSingleResult();
    }

    /**
     * Đếm tổng số users (giả sử có bảng User)
     */
    public long getTotalUsers() {
        try {
            String jpql = "SELECT COUNT(u) FROM User u";
            return entityManager.createQuery(jpql, Long.class).getSingleResult();
        } catch (Exception e) {
            // Nếu không có bảng User, trả về 0
            return 0;
        }
    }

}
